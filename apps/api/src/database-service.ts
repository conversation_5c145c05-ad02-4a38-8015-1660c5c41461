// src/database-service.ts

/**
 * Dieses Modul kapselt alle Datenbankinteraktionen für die D1-Datenbank
 * unter Verwendung von Drizzle ORM.
 */

import { drizzle } from "drizzle-orm/d1";
import { eq, and, desc, or, lt, isNull, inArray } from "drizzle-orm";
import type { DrizzleD1Database } from "drizzle-orm/d1";
import * as schema from "@socialfeed/drizzle-schema/d1";
import { sha256 } from "@noble/hashes/sha256";
import { bytesToHex } from "@noble/hashes/utils";

// Helper function to hash API keys
export function hashApiKey(apiKey: string): string {
  const hash = sha256(apiKey);
  return bytesToHex(hash);
}
import {
  PlatformConnection,
  Post,
  InsertPost,
  Platform,
  GraphApiMedia,
} from "./types";

// --- Typ für den Drizzle D1 Client ---
type DbClient = DrizzleD1Database<typeof schema>;

// --- Initialisierung ---

/**
 * Erstellt und gibt eine Drizzle D1 Client Instanz zurück.
 * @param d1 - Die D1 Datenbank Binding aus der Umgebung.
 * @returns Eine initialisierte Drizzle Client Instanz.
 */
export function getDbClient(d1: D1Database): DbClient {
  // Stelle sicher, dass das Schema übergeben wird, damit Typen korrekt funktionieren
  return drizzle(d1, { schema });
}

// --- Platform Connections ---

/**
 * Findet alle aktiven PlatformConnection IDs für einen gegebenen Plattform-Nutzer.
 * Nützlich, um eingehende Webhooks den richtigen internen Verbindungen zuzuordnen.
 * @param db - Der Drizzle DB Client.
 * @param platform - Die Plattform ('instagram', 'facebook', etc.).
 * @param platformUserId - Die User ID auf der externen Plattform.
 * @returns Ein Array von Objekten mit der internen Connection ID.
 */
export async function findActiveConnectionsByPlatformUserId(
  db: DbClient,
  platform: Platform,
  platformUserId: string
): Promise<{ id: string }[]> {
  try {
    return await db
      .select({ id: schema.platformConnections.id })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.platformConnections.platform, platform),
          eq(schema.platformConnections.platformAccountId, platformUserId),
          eq(schema.platformConnections.isActive, true)
        )
      )
      .all();
  } catch (e) {
    console.error(
      `DB_SERVICE: Error finding active connections for ${platform} user ${platformUserId}:`,
      e
    );
    throw e; // Fehler weiterleiten für besseres Debugging im Aufrufer
  }
}

/**
 * Holt alle Details für eine spezifische Platform Connection anhand ihrer internen ID.
 * @param db - Der Drizzle DB Client.
 * @param platformConnectionId - Die interne ID der Platform Connection.
 * @returns Das PlatformConnection Objekt oder null, wenn nicht gefunden.
 */
export async function getConnectionDetails(
  db: DbClient,
  platformConnectionId: string
): Promise<PlatformConnection | null> {
  try {
    const result = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, platformConnectionId))
      .get(); // .get() holt max. 1 Ergebnis
    return result ?? null;
  } catch (e) {
    console.error(
      `DB_SERVICE: Error getting connection details for ${platformConnectionId}:`,
      e
    );
    throw e;
  }
}

/**
 * Aktualisiert den Status einer Platform Connection und setzt den `lastCheckedAt` Zeitstempel.
 * @param db - Der Drizzle DB Client.
 * @param platformConnectionId - Die ID der zu aktualisierenden Connection.
 * @param status - Der neue Status ('active', 'inactive', 'auth_needed', 'expired').
 */
export async function updateConnectionStatus(
  db: DbClient,
  platformConnectionId: string,
  status: {
    isActive?: boolean;
    hasError?: boolean;
    isConnected?: boolean;
    needsReconnect?: boolean;
  }
): Promise<void> {
  try {
    const result = await db
      .update(schema.platformConnections)
      .set({
        lastCheckedAt: new Date(),
        ...status,
      }) // lastCheckedAt immer aktualisieren
      .where(eq(schema.platformConnections.id, platformConnectionId))
      .run(); // .run() für Updates ohne Rückgabewert in D1
    console.log(
      `DB_SERVICE: Updated status for connection ${platformConnectionId} to ${status}. Rows affected: ${result.meta.changes}`
    );
  } catch (e) {
    console.error(
      `DB_SERVICE: Failed to update status for connection ${platformConnectionId} to ${status}:`,
      e
    );
    // Fehler loggen, aber nicht unbedingt weiterwerfen, um den aufrufenden Prozess (z.B. Scheduled Handler) nicht komplett abzubrechen.
    // LogErrorToAnalytics könnte hier aufgerufen werden.
  }
}

/**
 * Holt alle aktiven Instagram Connections (Business und Instagram through Facebook).
 * @param db - Der Drizzle DB Client.
 * @returns Ein Array von Objekten mit id, platformAccountId und verschlüsseltem Token.
 */
export async function getActiveInstagramConnections(
  db: DbClient
): Promise<PlatformConnection[]> {
  try {
    return await db
      .select()
      .from(schema.platformConnections)
      .where(
        and(
          or(
            eq(schema.platformConnections.platform, "instagram_business"),
            eq(schema.platformConnections.platform, "instagram_with_facebook")
          ),
          eq(schema.platformConnections.isActive, true)
        )
      )
      .all();
  } catch (e) {
    console.error(`DB_SERVICE: Error getting active instagram connections:`, e);
    throw e;
  }
}
/**
 * Holt alle aktiven TikTok Connections.
 * @param db - Der Drizzle DB Client.
 * @returns Ein Array von Objekten mit der Connection ID.
 */
export async function getActiveTikTokConnections(
  db: DbClient
): Promise<{ id: string }[]> {
  try {
    return await db
      .select({ id: schema.platformConnections.id })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.platformConnections.platform, "tiktok"),
          eq(schema.platformConnections.isActive, true)
        )
      )
      .all();
  } catch (e) {
    console.error(`DB_SERVICE: Error getting active tiktok connections:`, e);
    throw e;
  }
}

/**
 * Holt alle aktiven YouTube Connections.
 * @param db - Der Drizzle DB Client.
 * @returns Ein Array von Objekten mit der Connection ID.
 */
export async function getActiveYouTubeConnections(
  db: DbClient
): Promise<{ id: string }[]> {
  try {
    return await db
      .select({ id: schema.platformConnections.id })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.platformConnections.platform, "youtube"),
          eq(schema.platformConnections.isActive, true)
        )
      )
      .all();
  } catch (e) {
    console.error(`DB_SERVICE: Error getting active youtube connections:`, e);
    throw e;
  }
}

/**
 * Holt alle aktiven Facebook Connections.
 * @param db - Der Drizzle DB Client.
 * @returns Ein Array von Objekten mit der Connection ID.
 */
export async function getActiveFacebookConnections(
  db: DbClient
): Promise<{ id: string }[]> {
  try {
    return await db
      .select({ id: schema.platformConnections.id })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.platformConnections.platform, "facebook"),
          eq(schema.platformConnections.isActive, true)
        )
      )
      .all();
  } catch (e) {
    console.error(`DB_SERVICE: Error getting active facebook connections:`, e);
    throw e;
  }
}

/**
 * Holt alle aktiven Instagram/Facebook Connections, deren Tokens überprüft werden müssen.
 * @param db - Der Drizzle DB Client.
 * @param checkThreshold - Zeitstempel (ms), nur Tokens prüfen, die älter als dieser sind.
 * @returns Ein Array von Connection-Objekten mit den für den Check nötigen Feldern.
 */
export async function getConnectionsForTokenCheck(
  db: DbClient,
  checkThreshold: number
): Promise<
  Pick<
    PlatformConnection,
    "id" | "platform" | "accessTokenEncrypted" | "tokenExpiresAt"
  >[]
> {
  try {
    return await db
      .select({
        id: schema.platformConnections.id,
        platform: schema.platformConnections.platform,
        accessTokenEncrypted: schema.platformConnections.accessTokenEncrypted,
        tokenExpiresAt: schema.platformConnections.tokenExpiresAt,
      })
      .from(schema.platformConnections)
      .where(
        and(
          or(
            // Instagram, Instagram Business, Instagram with Facebook, Facebook, oder YouTube (TikTok hat ggf. andere Logik)
            eq(schema.platformConnections.platform, "instagram"),
            eq(schema.platformConnections.platform, "instagram_business"),
            eq(schema.platformConnections.platform, "instagram_with_facebook"),
            eq(schema.platformConnections.platform, "facebook"),
            eq(schema.platformConnections.platform, "youtube")
          ),
          eq(schema.platformConnections.isActive, true), // Nur aktive prüfen
          or(
            // Wenn lange nicht geprüft ODER bald abläuft
            isNull(schema.platformConnections.lastCheckedAt),
            lt(
              schema.platformConnections.lastCheckedAt,
              new Date(checkThreshold)
            )
            // Optional: Ablaufdatum prüfen (hier weggelassen für Einfachheit)
            // lt(schema.platformConnections.tokenExpiresAt, new Date(now + 5 * 24 * 60 * 60 * 1000))
          )
        )
      )
      .all();
  } catch (e) {
    console.error(`DB_SERVICE: Error getting connections for token check:`, e);
    throw e;
  }
}

// --- API Keys ---

/**
 * Validates an API Key against the database.
 * @param db - The Drizzle DB Client.
 * @param apiKey - The API Key to validate.
 * @returns An object containing validation results and feed information.
 */
export async function validateApiKey(
  db: DbClient,
  apiKey: string
): Promise<{
  isValid: boolean;
  feedId?: string;
  organizationId?: string;
  error?: string;
  status?: number;
}> {
  try {
    // Select specific fields
    const keyRecord = await db
      .select({
        feedId: schema.apiKeys.feedId,
        expiresAt: schema.apiKeys.expiresAt,
        isActive: schema.apiKeys.isActive,
      })
      .from(schema.apiKeys)
      .where(eq(schema.apiKeys.key, hashApiKey(apiKey)))
      .get();

    if (!keyRecord) {
      return { isValid: false, error: "Invalid API key", status: 401 };
    }
    if (keyRecord.isActive === false) {
      return { isValid: false, error: "API key is inactive", status: 403 };
    }
    if (keyRecord.expiresAt && keyRecord.expiresAt < new Date()) {
      return { isValid: false, error: "API key has expired", status: 403 };
    }

    // Get the organization ID for the feed
    const feedRecord = await db
      .select({ organizationId: schema.projects.organizationId })
      .from(schema.feeds)
      .where(eq(schema.feeds.id, keyRecord.feedId))
      .rightJoin(
        schema.projects,
        eq(schema.feeds.projectId, schema.projects.id)
      )
      .get();

    if (!feedRecord) {
      return { isValid: false, error: "Feed not found", status: 404 };
    }

    return {
      isValid: true,
      feedId: keyRecord.feedId,
      organizationId: feedRecord.organizationId,
    };
  } catch (e) {
    console.error("DB_SERVICE: API key validation DB error:", e);
    return {
      isValid: false,
      error: "Internal server error during authentication",
      status: 500,
    };
  }
}

// --- Posts & Comments ---

/**
 * Fügt einen neuen Post ein oder aktualisiert einen bestehenden Post
 * basierend auf platformConnectionId und mediaId (Upsert).
 * @param db - Der Drizzle DB Client.
 * @param postData - Die einzufügenden/aktualisierenden Post-Daten.
 */
export async function upsertPost(
  db: DbClient,
  postData: InsertPost,
  children?: GraphApiMedia["children"]
): Promise<void> {
  try {
    // First, try to find existing post
    const existingPost = await db
      .select({ id: schema.posts.id })
      .from(schema.posts)
      .where(
        and(
          eq(schema.posts.platformConnectionId, postData.platformConnectionId),
          eq(schema.posts.mediaId, postData.mediaId)
        )
      )
      .get();

    let postId: string;

    if (existingPost) {
      // Update existing post
      await db
        .update(schema.posts)
        .set({
          likeCount: postData.likeCount,
          commentsCount: postData.commentsCount,
          caption: postData.caption,
          mediaUrl: postData.mediaUrl,
          mediaType: postData.mediaType,
          permalink: postData.permalink,
          lastWebhookUpdate: postData.lastWebhookUpdate,
          lastFetched: postData.lastFetched,
        })
        .where(eq(schema.posts.id, existingPost.id));

      postId = existingPost.id;
    } else {
      // Insert new post
      const insertResult = await db
        .insert(schema.posts)
        .values(postData)
        .returning({ id: schema.posts.id });

      postId = insertResult[0].id;
    }

    if (postData.mediaType === "CAROUSEL_ALBUM" && children?.data) {
      const mediaItemsToInsert = children.data
        .map((child, index) => ({
          postId: postId, // Use the actual post ID now
          mediaUrl: child.media_url,
          mediaType: child.media_type,
          displayOrder: index,
        }))
        .filter(
          (
            item
          ): item is typeof item & { mediaUrl: string; mediaType: string } =>
            item.mediaUrl != null && item.mediaType != null
        );

      if (mediaItemsToInsert.length > 0) {
        // Delete old items and insert new ones
        await db
          .delete(schema.postMediaItems)
          .where(eq(schema.postMediaItems.postId, postId));

        await db.insert(schema.postMediaItems).values(mediaItemsToInsert);
      }
    }
  } catch (e) {
    console.error(
      `DB_SERVICE: Error upserting post ${postData.mediaId} for conn ${
        postData.platformConnectionId
      }:`,
      e
    );
    throw e; // Propagate error for the caller to handle
  }
}

/**
 * Holt die letzten Posts für einen bestimmten Feed, optional gefiltert nach Plattform.
 * @param db - Der Drizzle DB Client.
 * @param feedId - Die ID des Feeds.
 * @param limit - Die maximale Anzahl der zurückzugebenden Posts.
 * @param platformFilter - Optional: Nur Posts von dieser Plattform holen.
 * @returns Ein Array von Post-Objekten.
 */
export async function getPostsForFeed(
  db: DbClient,
  feedId: string,
  limit: number,
  platformFilter?: Platform
): Promise<Post[]> {
  try {
    const connections = await db
      .select({ id: schema.platformConnections.id })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.feedConnections.feedId, feedId),
          eq(schema.platformConnections.isActive, true),
          platformFilter
            ? eq(schema.platformConnections.platform, platformFilter)
            : undefined
        )
      )
      .innerJoin(
        schema.feedConnections,
        eq(
          schema.platformConnections.id,
          schema.feedConnections.platformConnectionId
        )
      )
      .all();

    if (connections.length === 0) {
      return [];
    }
    const connectionIds = connections.map((conn) => conn.id);

    const postsData = await db
      .select()
      .from(schema.posts)
      .where(inArray(schema.posts.platformConnectionId, connectionIds))
      .orderBy(desc(schema.posts.timestamp))
      .limit(limit)
      .all();

    if (postsData.length === 0) {
      return [];
    }

    // Extract post IDs for the media item query
    const postIds = postsData.map((p) => p.id);

    // Fetch all media items for the retrieved posts
    const mediaItems = await db
      .select()
      .from(schema.postMediaItems)
      .where(inArray(schema.postMediaItems.postId, postIds))
      .orderBy(schema.postMediaItems.displayOrder)
      .all();

    // Map media items to their respective posts
    const postsWithMedia: Post[] = postsData.map((post) => ({
      ...post,
      mediaItems: mediaItems.filter((item) => item.postId === post.id),
    }));

    return postsWithMedia;
  } catch (e) {
    console.error(`DB_SERVICE: Error getting posts for feed ${feedId}:`, e);
    throw e;
  }
}

/**
 * Checks if a feed belongs to an organization
 * @param db - The Drizzle DB Client
 * @param feedId - The ID of the feed
 * @param organizationId - The organization ID from PropelAuth
 * @returns true if the feed belongs to the organization
 */
export async function isFeedOwnedByOrganization(
  db: DbClient,
  feedId: string,
  organizationId: string
): Promise<boolean> {
  try {
    const result = await db
      .select({ orgId: schema.projects.organizationId })
      .from(schema.feeds)
      .where(eq(schema.feeds.id, feedId))
      .innerJoin(
        schema.projects,
        eq(schema.feeds.projectId, schema.projects.id)
      )
      .get();

    return result?.orgId === organizationId;
  } catch (e) {
    console.error(
      `DB_SERVICE: Error checking feed ownership for feed ${feedId} and org ${organizationId}:`,
      e
    );
    return false;
  }
}

// --- TikTok Locking Functions ---

/**
 * Attempts to acquire a lock for a TikTok account.
 * @param db - The Drizzle DB Client.
 * @param platformConnectionId - The ID of the platform connection to lock.
 * @returns true if lock was acquired, false if already locked.
 */
export async function acquireTikTokLock(
  db: DbClient,
  platformConnectionId: string
): Promise<boolean> {
  try {
    // Try to update the lockedAt field only if it's currently null
    const result = await db
      .update(schema.platformConnections)
      .set({ lockedAt: new Date() })
      .where(
        and(
          eq(schema.platformConnections.id, platformConnectionId),
          isNull(schema.platformConnections.lockedAt)
        )
      )
      .run();

    // If changes > 0, we successfully acquired the lock
    return result.meta.changes > 0;
  } catch (e) {
    console.error(
      `DB_SERVICE: Error acquiring TikTok lock for connection ${platformConnectionId}:`,
      e
    );
    return false;
  }
}

/**
 * Releases a lock for a TikTok account.
 * @param db - The Drizzle DB Client.
 * @param platformConnectionId - The ID of the platform connection to unlock.
 */
export async function releaseTikTokLock(
  db: DbClient,
  platformConnectionId: string
): Promise<void> {
  try {
    await db
      .update(schema.platformConnections)
      .set({ lockedAt: null })
      .where(eq(schema.platformConnections.id, platformConnectionId))
      .run();
  } catch (e) {
    console.error(
      `DB_SERVICE: Error releasing TikTok lock for connection ${platformConnectionId}:`,
      e
    );
  }
}

/**
 * Cleans up stale locks (older than 10 minutes).
 * @param db - The Drizzle DB Client.
 */
export async function cleanupStaleTikTokLocks(db: DbClient): Promise<void> {
  try {
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
    await db
      .update(schema.platformConnections)
      .set({ lockedAt: null })
      .where(
        and(
          eq(schema.platformConnections.platform, "tiktok"),
          lt(schema.platformConnections.lockedAt, tenMinutesAgo)
        )
      )
      .run();
    console.log("DB_SERVICE: Cleaned up stale TikTok locks");
  } catch (e) {
    console.error("DB_SERVICE: Error cleaning up stale TikTok locks:", e);
  }
}

/**
 * Checks if a connection belongs to an organization via its feed
 * @param db - The Drizzle DB Client
 * @param connectionId - The ID of the platform connection
 * @param organizationId - The organization ID from PropelAuth
 * @returns true if the connection belongs to the organization
 */
export async function isConnectionOwnedByOrganization(
  db: DbClient,
  connectionId: string,
  organizationId: string
): Promise<boolean> {
  try {
    const result = await db
      .select({ orgId: schema.projects.organizationId })
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.id, connectionId))
      .leftJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .get();

    return result?.orgId === organizationId;
  } catch (e) {
    console.error(
      `DB_SERVICE: Error checking connection ownership for connection ${connectionId} and org ${organizationId}:`,
      e
    );
    return false;
  }
}
